# Mareo Agent

## Development Setup

You need to have poetry>=1.8 installed, nothing more. Then just simply run the script, and you are good to go!

```shell
cd mareo
bash scripts/setup.sh
```

## run

You need a few environment variables to run the app, they are:

```dotenv
LANGFUSE_HOST=https://us.cloud.langfuse.com
LANGFUSE_PUBLIC_KEY=<langfuse_public_key>
LANGFUSE_SECRET_KEY=<langfuse_secret_key>
OPENROUTER_API_KEY=<openrouter_api_key>
TAVILY_KEY=<tavily_key>
TAVILY_BASE=https://mcp-tavily.mareo.ai/mcp
```
Then you can run the app via poetry:
```shell
poetry run uvicorn mareo.app.server:app --host 0.0.0.0 --port 3000
```

## build docker image

```shell
docker build -f containers/mareo/Dockerfile . -t <tag_name>
```

## 开发流程

1. 拉取`main`分支最新代码，基于需求新建开发分支`<your_branch_name>`，并在本地验证开发效果
    1. 分支命名要求有意义，尽量描述需求本身
    2. 分支名需根据需求以`feat/fix/chore/docs`等开头
    3. 禁止直接向`main`分支提交代码
2. 提交PR，`<your_branch_name>` -> `main`，此时github 流水线会做以下事情：
    - 用ruff lint基于`./dev_config/ruff.toml`作为配置文件检查代码规范
    - 用pytest测试代码
    - 上述任务通过后，会：
      - 以当前commit的SHA作为tag去构建docker image并推送，如`ghcr.io/sihai-org/mareo:<full_commit_sha>`
      - 构建完成后会操作k8s集群配置ingress、service和deployment等资源，并在`pr-<pull_request_id>.next.mareo.ai`部署刚刚的服务
3. 此时开发者应当在该测试环境下验证功能，如果有任何问题，则：
    - 在本地修复问题并在本地验证问题是否被解决
    - 提交新的commit到PR所在的分支`<your_branch_name>`
    - PR会自动重新触发步骤2的构建和部署
4. 一切正常后，开发者需要merge该PR到`main`分支，合并完成后此PR即close，此时流水线会做以下事情：
    - 移除刚刚的部署在k8s上的所有资源
    - 基于合并后的commit SHA再次构建docker image，同时基于`ref_name`(分支名如`main`或者git tag如`release-v0.1.0`)打上image tag。
    - 使用最新的image去滚动更新k8s集群上的mareo-next的deployment
5. 开发者需要继续验证线上环境的功能是否正常。

## 发布流程

1. 开发累积一定功能后，可以基于版本号发布新的release，trigger是在主分支上打tag（推荐通过github release的方式）
2. 版本号遵从`v<MAJOR>.<MINOR>.<PATCH>`的语义化版本规范
    - MAJOR：大版本，通常是破坏性变更
    - MINOR：小版本，通常是新增功能
    - PATCH：补丁版本，通常是修复bug
3. 打tag后，github流水线会自动触发以下操作：
    - 以当前tag作为版本号，构建docker image并推送，如`ghcr.io/sihai-org/mareo:v1.2.5`
    - 操作k8s集群，滚动更新prod环境的mareo
4. 新版本发布后，需要密切关注prod环境的运行状态，确保没有问题，如果有问题，随时基于版本号回滚到上一个版本。