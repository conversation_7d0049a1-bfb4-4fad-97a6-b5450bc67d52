name: image build and publish

# Always run on "next-main"
# Can also be triggered manually
on:
  workflow_call:
    inputs:
      image-name:
        description: 'Name of the image to build'
        required: true
        type: string
      dockerfile-path:
        description: 'Path to the Dockerfile'
        required: true
        type: string
      context-path:
        description: 'Path to the build context'
        required: false
        type: string
        default: '.'
      tags:
        description: 'Tags to apply to the image, delimited by new-line'
        required: true
        type: string

jobs:
  build_image:
    name: Build Image
    runs-on: ubuntu-latest
    steps:
      - name: re-format tags
        run: |
          tags="${{ inputs.tags }}"
          for tag in $tags; do
            prefixed_tags+="ghcr.io/${{ github.repository_owner }}/${{ inputs.image-name }}:$tag\n";
          done;
          echo -e "Tags to be used:\n$prefixed_tags"
          {
            echo 'prefixed_tags<<EOF'
            echo -e ${prefixed_tags}
            echo EOF
          } >> $GITHUB_ENV

      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha || github.sha }}

      - name: Login to GHCR
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and Push Image
        uses: docker/build-push-action@v6
        with:
          context: ${{ inputs.context-path }}
          file: ${{ inputs.dockerfile-path }}
          push: true
          cache-from: type=gha
          cache-to: type=gha,mode=max
          tags: ${{ env.prefixed_tags }}
