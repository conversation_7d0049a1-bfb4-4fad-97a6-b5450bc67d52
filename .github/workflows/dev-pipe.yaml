name: Development CI/CD Pipeline

permissions:
  pull-requests: read
  contents: read

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main
    tags:
      - 'release-v[0-9]+.[0-9]+.[0-9]+.?*'
      - 'hotfix-v[0-9]+.[0-9]+.[0-9]+.?*'
  workflow_dispatch:
    inputs:
      reason:
        description: 'Reason for manual trigger'
        required: true
        default: ''

# If triggered by a PR, it will be in the same group. However, each commit on main will be in its own unique group
concurrency:
  group: ${{ github.workflow }}-${{ (github.head_ref && github.ref) || github.run_id }}
  cancel-in-progress: true

jobs:
  changes:
    name: Detect File Changes
    runs-on: ubuntu-latest
    outputs:
      mareo: ${{ steps.filter.outputs.mareo }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            mareo:
              - 'containers/mareo/**'
              - 'mareo/**'
              - 'frontend/**'
              - 'poetry.lock'

  lint-check:
    name: <PERSON>t <PERSON>
    uses: ./.github/workflows/lint-check.yaml

  pytest:
    name: Run Pytest
    needs: [ lint-check ]
    uses: ./.github/workflows/pytest.yaml
    secrets: inherit

  build_mareo_via_pr:
    name: Build Mareo Image for PR
    needs: [ changes, lint-check, pytest ]
    permissions:
      packages: write
      pull-requests: read
      contents: read
    if: ${{ (needs.changes.outputs.mareo == 'true' && github.event_name == 'pull_request') || github.event_name == 'workflow_dispatch' }}
    uses: ./.github/workflows/build.yaml
    with:
      dockerfile-path: 'containers/mareo/Dockerfile'
      image-name: 'mareo'
      context-path: '.'
      tags: |
        ${{  github.sha }}
        pr-${{ github.event.pull_request.number }}

  build_mareo_via_main:
    name: Build Mareo Image for Main
    needs: [ changes, lint-check, pytest ]
    permissions:
      packages: write
      pull-requests: read
      contents: read
    if: ${{ needs.changes.outputs.mareo == 'true' && github.event_name == 'push' }}
    uses: ./.github/workflows/build.yaml
    with:
      dockerfile-path: 'containers/mareo/Dockerfile'
      image-name: 'mareo'
      context-path: '.'
      tags: |
        ${{ github.sha }}
        ${{ github.ref_name }}
        latest
