# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import uuid
from unittest.mock import AsyncMock, Mock

import pytest
from google.adk.agents import InvocationContext
from google.adk.events import Event
from google.adk.models import LlmResponse
from google.genai.types import ModelContent, Part

from mareo.common.agents.LangGraphWrapperAgent import LangGraphWrapperAgent


@pytest.fixture
def mock_compiled_graph():
    """Mock LangGraph compiled graph"""
    graph = Mock()
    graph.call_count = 0
    graph.call_args = None

    # Mock the astream method to return async generator
    async def mock_astream_impl(inputs, stream_mode=None, config=None):
        # Track calls manually
        graph.call_count += 1
        graph.call_args = {'inputs': inputs, 'stream_mode': stream_mode, 'config': config}

        # Simulate LangGraph streaming updates
        updates = [
            ('node1', {'output': 'Step 1 complete', 'intermediate': 'processing...'}),
            ('node2', {'output': 'Final answer: Hello World!', 'status': 'completed'}),
        ]

        for node_name, update in updates:
            yield (None, {node_name: update})

    graph.astream = mock_astream_impl
    return graph


@pytest.fixture
def mock_session():
    """Mock session with state"""
    session = Mock()
    session.state = {}
    return session


@pytest.fixture
def mock_user_content():
    """Mock user content"""
    content = Mock()
    content.parts = [Mock(text='Test user input')]
    return content


@pytest.fixture
def mock_context(mock_session, mock_user_content):
    """Mock InvocationContext"""
    context = Mock(spec=InvocationContext)
    context.session = mock_session
    context.user_content = mock_user_content
    context.invocation_id = 'test-invocation-id'
    context.branch = 'test-branch'
    context.agent = Mock()
    context.agent.name = 'test_agent'  # Set name as string, not Mock
    return context


class TestLangGraphWrapperAgent:
    """Test cases for LangGraphWrapperAgent"""

    def test_init_with_default_params(self, mock_compiled_graph):
        """Test initialization with default parameters"""
        agent = LangGraphWrapperAgent(name='test_agent', compiled_graph=mock_compiled_graph)

        assert agent.name == 'test_agent'
        assert agent._graph == mock_compiled_graph
        assert agent._output_key is None
        assert agent._stream_modes == ['updates']

    def test_init_with_custom_params(self, mock_compiled_graph):
        """Test initialization with custom parameters"""
        agent = LangGraphWrapperAgent(
            name='custom_agent',
            compiled_graph=mock_compiled_graph,
            output_key='custom_output',
            stream_modes=['debug', 'updates'],
        )

        assert agent.name == 'custom_agent'
        assert agent._graph == mock_compiled_graph
        assert agent._output_key == 'custom_output'
        assert agent._stream_modes == ['debug', 'updates']

    @pytest.mark.asyncio
    async def test_run_async_impl_new_thread(self, mock_compiled_graph, mock_context):
        """Test _run_async_impl with new thread creation"""
        agent = LangGraphWrapperAgent(name='test_agent', compiled_graph=mock_compiled_graph)

        # Ensure no existing thread_id
        mock_context.session.state = {}

        events = []
        async for event in agent._run_async_impl(mock_context):
            events.append(event)

        # Verify thread_id was created and stored
        assert 'lg_thread_id' in mock_context.session.state
        thread_id = mock_context.session.state['lg_thread_id']
        assert isinstance(thread_id, str)
        assert len(thread_id) > 0

        # Verify graph was called with correct parameters
        assert mock_compiled_graph.call_count == 1
        call_args = mock_compiled_graph.call_args

        assert call_args['inputs'] == {'input': 'Test user input'}
        assert call_args['stream_mode'] == ['updates']
        assert call_args['config'] == {'configurable': {'thread_id': thread_id}}

        # Verify events were generated
        assert len(events) == 3  # 2 thought events + 1 final text event

        # Check thought events
        thought_events = events[:2]
        for event in thought_events:
            assert isinstance(event, Event)
            assert event.author == 'test_agent'
            assert event.invocation_id == 'test-invocation-id'
            assert event.branch == 'test-branch'

            # Check if it's a thought event
            assert hasattr(event, 'content')
            assert len(event.content.parts) == 1
            assert event.content.parts[0].thought is True

        # Check final text event
        final_event = events[-1]
        assert hasattr(final_event, 'content')
        assert len(final_event.content.parts) == 1
        assert final_event.content.parts[0].text == 'Final answer: Hello World!'
        assert not hasattr(final_event.content.parts[0], 'thought') or not final_event.content.parts[0].thought

    @pytest.mark.asyncio
    async def test_run_async_impl_existing_thread(self, mock_compiled_graph, mock_context):
        """Test _run_async_impl with existing thread_id"""
        agent = LangGraphWrapperAgent(name='test_agent', compiled_graph=mock_compiled_graph)

        # Set existing thread_id
        existing_thread_id = 'existing-thread-123'
        mock_context.session.state = {'lg_thread_id': existing_thread_id}

        events = []
        async for event in agent._run_async_impl(mock_context):
            events.append(event)

        # Verify existing thread_id was used
        assert mock_context.session.state['lg_thread_id'] == existing_thread_id

        # Verify graph was called with existing thread_id
        call_args = mock_compiled_graph.call_args
        assert call_args['config'] == {'configurable': {'thread_id': existing_thread_id}}

    @pytest.mark.asyncio
    async def test_run_async_impl_custom_output_key(self, mock_context):
        """Test _run_async_impl with custom output_key"""
        mock_graph = Mock()

        async def mock_astream_custom(inputs, stream_mode=None, config=None):
            yield (None, {'final_node': {'custom_output': 'Custom final answer'}})

        mock_graph.astream = mock_astream_custom

        agent = LangGraphWrapperAgent(name='test_agent', compiled_graph=mock_graph, output_key='custom_output')

        events = []
        async for event in agent._run_async_impl(mock_context):
            events.append(event)

        # Verify final event contains custom output
        final_event = events[-1]
        assert hasattr(final_event, 'content')
        assert final_event.content.parts[0].text == 'Custom final answer'

    @pytest.mark.asyncio
    async def test_run_async_impl_fallback_output_keys(self, mock_context):
        """Test _run_async_impl with fallback to default output keys"""
        mock_graph = Mock()

        async def mock_astream_fallback(inputs, stream_mode=None, config=None):
            yield (None, {'final_node': {'answer': 'Answer from fallback key'}})

        mock_graph.astream = mock_astream_fallback

        agent = LangGraphWrapperAgent(name='test_agent', compiled_graph=mock_graph)

        events = []
        async for event in agent._run_async_impl(mock_context):
            events.append(event)

        # Verify fallback to 'answer' key worked
        final_event = events[-1]
        assert hasattr(final_event, 'content')
        assert final_event.content.parts[0].text == 'Answer from fallback key'

    @pytest.mark.asyncio
    async def test_run_async_impl_no_output_key_fallback(self, mock_context):
        """Test _run_async_impl when no recognized output key is found"""
        mock_graph = Mock()

        async def mock_astream_no_output(inputs, stream_mode=None, config=None):
            yield (None, {'final_node': {'some_other_key': 'other data', 'status': 'done'}})

        mock_graph.astream = mock_astream_no_output

        agent = LangGraphWrapperAgent(name='test_agent', compiled_graph=mock_graph)

        events = []
        async for event in agent._run_async_impl(mock_context):
            events.append(event)

        # Verify fallback to string representation of final_state
        final_event = events[-1]
        assert hasattr(final_event, 'content')
        final_text = final_event.content.parts[0].text

        # Should contain the full state as string
        assert 'some_other_key' in final_text
        assert 'other data' in final_text
        assert 'done' in final_text

    @pytest.mark.asyncio
    async def test_run_async_impl_custom_stream_modes(self, mock_context):
        """Test _run_async_impl with custom stream modes"""
        mock_graph = Mock()
        mock_graph.call_count = 0
        mock_graph.call_args = None

        async def mock_astream_modes(inputs, stream_mode=None, config=None):
            mock_graph.call_count += 1
            mock_graph.call_args = {'inputs': inputs, 'stream_mode': stream_mode, 'config': config}
            yield (None, {'node': {'output': 'test output'}})

        mock_graph.astream = mock_astream_modes

        agent = LangGraphWrapperAgent(name='test_agent', compiled_graph=mock_graph, stream_modes=['debug', 'values'])

        events = []
        async for event in agent._run_async_impl(mock_context):
            events.append(event)

        # Verify custom stream modes were used
        call_args = mock_graph.call_args
        assert call_args['stream_mode'] == ['debug', 'values']

    @pytest.mark.asyncio
    async def test_run_async_impl_multiple_updates(self, mock_context):
        """Test _run_async_impl with multiple node updates"""
        mock_graph = Mock()

        async def mock_astream_multiple(inputs, stream_mode=None, config=None):
            updates = [
                ('node1', {'status': 'starting', 'data': 'initial'}),
                ('node2', {'status': 'processing', 'data': 'intermediate'}),
                ('node3', {'status': 'completing', 'data': 'partial'}),
                ('node4', {'output': 'Final result', 'status': 'done'}),
            ]

            for node_name, update in updates:
                yield (None, {node_name: update})

        mock_graph.astream = mock_astream_multiple

        agent = LangGraphWrapperAgent(name='test_agent', compiled_graph=mock_graph)

        events = []
        async for event in agent._run_async_impl(mock_context):
            events.append(event)

        # Should have 4 thought events + 1 final text event
        assert len(events) == 5

        # Check thought events
        thought_events = events[:4]
        expected_messages = [
            "[node1] {'status': 'starting', 'data': 'initial'}",
            "[node2] {'status': 'processing', 'data': 'intermediate'}",
            "[node3] {'status': 'completing', 'data': 'partial'}",
            "[node4] {'output': 'Final result', 'status': 'done'}",
        ]

        for i, event in enumerate(thought_events):
            assert hasattr(event, 'content')
            assert event.content.parts[0].text == expected_messages[i]
            assert event.content.parts[0].thought is True

        # Check final event
        final_event = events[-1]
        assert hasattr(final_event, 'content')
        assert final_event.content.parts[0].text == 'Final result'

    @pytest.mark.asyncio
    async def test_run_async_impl_empty_stream(self, mock_context):
        """Test _run_async_impl with empty stream"""
        mock_graph = Mock()

        async def mock_astream_empty(inputs, stream_mode=None, config=None):
            # Empty generator
            if False:
                yield

        mock_graph.astream = mock_astream_empty

        agent = LangGraphWrapperAgent(name='test_agent', compiled_graph=mock_graph)

        events = []
        async for event in agent._run_async_impl(mock_context):
            events.append(event)

        # Should only have final event with empty state fallback
        assert len(events) == 1
        final_event = events[0]
        assert hasattr(final_event, 'content')
        assert final_event.content.parts[0].text == '{}'

    def test_event_properties(self, mock_compiled_graph):
        """Test that generated events have correct properties"""
        agent = LangGraphWrapperAgent(name='test_agent', compiled_graph=mock_compiled_graph)

        # Create a test event using the same pattern as the agent
        test_event = Event(
            id=Event.new_id(),
            invocation_id='test-invocation',
            author=agent.name,
            branch='test-branch',
        )

        llm_response = LlmResponse(content=ModelContent(parts=[Part(text='test', thought=True)]))
        combined_event = Event.model_validate(
            {
                **test_event.model_dump(exclude_none=True),
                **llm_response.model_dump(exclude_none=True),
            }
        )

        assert combined_event.author == 'test_agent'
        assert combined_event.invocation_id == 'test-invocation'
        assert combined_event.branch == 'test-branch'
        assert hasattr(combined_event, 'content')

    @pytest.mark.parametrize(
        'output_key, final_state, expected_answer',
        [
            ('custom_key', {'custom_key': 'custom answer'}, 'custom answer'),
            (None, {'output': 'output answer'}, 'output answer'),
            (None, {'answer': 'answer value'}, 'answer value'),
            (None, {'other_key': 'other value'}, "{'other_key': 'other value'}"),
            ('missing_key', {'existing_key': 'value'}, "{'existing_key': 'value'}"),
        ],
    )
    def test_output_key_resolution(self, output_key, final_state, expected_answer):
        """Test output key resolution logic"""
        from mareo.common.agents.LangGraphWrapperAgent import LangGraphWrapperAgent

        # Create agent with specified output_key
        agent = LangGraphWrapperAgent(name='test', compiled_graph=Mock(), output_key=output_key)

        # Simulate the key resolution logic from the agent
        key_order = [agent._output_key, 'output', 'answer']
        answer = None
        for k in key_order:
            if k and k in final_state:
                answer = final_state[k]
                break
        if answer is None:
            answer = str(final_state)

        assert answer == expected_answer

    @pytest.mark.asyncio
    async def test_thread_id_persistence(self, mock_compiled_graph, mock_context):
        """Test that thread_id persists across multiple calls"""
        agent = LangGraphWrapperAgent(name='test_agent', compiled_graph=mock_compiled_graph)

        # First call - should create new thread_id
        mock_context.session.state = {}
        events1 = []
        async for event in agent._run_async_impl(mock_context):
            events1.append(event)

        first_thread_id = mock_context.session.state['lg_thread_id']

        # Second call - should use existing thread_id
        events2 = []
        async for event in agent._run_async_impl(mock_context):
            events2.append(event)

        second_thread_id = mock_context.session.state['lg_thread_id']

        # Thread ID should be the same
        assert first_thread_id == second_thread_id

        # Both calls should have events
        assert len(events1) > 0
        assert len(events2) > 0

    @pytest.mark.asyncio
    async def test_user_input_extraction(self, mock_compiled_graph, mock_context):
        """Test user input extraction from context"""
        agent = LangGraphWrapperAgent(name='test_agent', compiled_graph=mock_compiled_graph)

        # Test with different user content
        mock_context.user_content.parts = [Mock(text='Complex user query with details')]

        events = []
        async for event in agent._run_async_impl(mock_context):
            events.append(event)

        # Verify the input passed to graph
        call_args = mock_compiled_graph.call_args
        assert call_args['inputs']['input'] == 'Complex user query with details'
