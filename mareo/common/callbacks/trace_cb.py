import logging
from typing import Optional

from google.adk.agents.callback_context import CallbackContext
from google.genai import types
from opentelemetry import trace

# It's good practice to use Python's logging module for traces
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


def trace_session_info(callback_context: CallbackContext) -> Optional[types.Content]:
    """
    A callback that logs the current user ID and session ID for tracing
    and updates the session state with this information.
    """
    # --- 1. Access Context Information ---
    # The CallbackContext object provides access to the current session.
    try:
        session = callback_context._invocation_context.session
    except Exception:
        session = None
    if not session:
        logger.warning('Trace callback: Session object not found in context.')
        return None  # Allow execution to continue

    user_id = session.user_id
    session_id = session.id

    trace.get_current_span().set_attribute('user.id', user_id)
    trace.get_current_span().set_attribute('session.id', session_id)
    return None
