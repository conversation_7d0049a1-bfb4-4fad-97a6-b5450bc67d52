from google.adk.agents import LlmAgent

from mareo.agent_hub.document_agent.agent import root_agent as document_agent
from mareo.agent_hub.search_agent.agent import root_agent as search_agent
from mareo.common.callbacks.trace_cb import trace_session_info
from mareo.common.models.lite_llm_reasoning import LiteLlmReasoning

# Create agent with tools - tools will be created in proper async context when needed
root_agent = LlmAgent(
    name='<PERSON><PERSON>',
    model=LiteLlmReasoning(model='openrouter/deepseek/deepseek-r1-0528', max_completion_tokens=16384),
    description='Agent to chat with user',
    instruction='You are a friendly assistant that chat with the user. Always respond in their language.'
    'You have specialized sub-agents: '
    "1. 'Mareo_search': Handles online searching. Delegate to it for these. "
    "Analyze the user's query. If it might require searching, delegate to 'Mareo_search'."
    "If it's just chat, handle it yourself. "
    "2. 'DocumentWriterAgent': Writes a document in markdown format and save it as an artifact."
    'After a search, always delegate to document writer to generate a md document.',
    # planner=MareoPlanner(thinking_config=ThinkingConfig(include_thoughts=True)),
    before_agent_callback=trace_session_info,
    sub_agents=[search_agent, document_agent],
)
