import os

from google.adk.agents import LlmAgent

from mareo.common.callbacks.trace_cb import trace_session_info
from mareo.common.models.lite_llm_reasoning import LiteLlmReasoning
from mareo.common.tools.mcp.tavily import get_tavily_toolset

tavily_key = os.getenv('TAVILY_KEY', '')

root_agent = LlmAgent(
    name='Mareo_search',
    model=LiteLlmReasoning(model='openrouter/deepseek/deepseek-r1-0528', max_completion_tokens=16384),
    description='Agent to search information online',
    instruction='You are a search agent to fetch information online. '
    'Perform searching in languages that is most likely to get better results.'
    'After getting enough information about the topic, transfer back to your parent agent by calling the transfer_to_agent tool. '
    'Do not summarize or output any information from the result, the parent agent is able to read it.',
    # planner=<PERSON>oPlanner(thinking_config=ThinkingConfig(include_thoughts=True)),
    before_agent_callback=trace_session_info,
    tools=[get_tavily_toolset()] if tavily_key else [],
)
