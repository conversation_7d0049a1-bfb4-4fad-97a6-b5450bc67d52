import asyncio
import logging
from typing import Optional

# ADK 核心组件
from google.adk.agents import LlmAgent
from google.adk.agents.callback_context import CallbackContext
from google.adk.artifacts import InMemoryArtifactService
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService

# Gemini 相关类型
from google.genai import types

# --- 1. 配置日志和常量 ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

APP_NAME = 'document_workflow_app'
USER_ID = 'doc_creator_01'
SESSION_ID = 'session_md_01'
GEMINI_MODEL = 'gemini-2.0-flash'
ARTIFACT_FILENAME = 'generated_document.md'


# --- 2. 定义回调函数：保存构件并转移控制权 ---
async def save_document_callback(callback_context: CallbackContext) -> Optional[types.Content]:
    """
    这是一个 after_agent_callback。它会在代理运行结束后被自动调用。
    此回调首先将代理的响应内容保存为一个构件，
    然后创建一个新的 Content 对象，其中包含一个函数调用，
    用于将控制权转移到 FinalizerAgent。
    """
    logger.info(f"--- [Callback] '{save_document_callback.__name__}' 已触发 ---")

    # 1. 从会话状态中读取由 output_key 保存的代理响应
    markdown_text = callback_context.state.get('generated_document_content')

    if markdown_text:
        logger.info(f'从会话状态中提取到 {len(markdown_text)} 字符的 Markdown 文本。')
        # 将文本封装成 ADK 的 Part 对象
        artifact_part = types.Part.from_text(text=markdown_text)
        try:
            # 使用上下文中的 save_artifact 方法保存构件
            version = await callback_context.save_artifact(filename=ARTIFACT_FILENAME, artifact=artifact_part)
            logger.info(f"成功将文档保存为构件 '{ARTIFACT_FILENAME}' (版本: {version})。")
        except Exception as e:
            logger.error(f'保存构件时出错: {e}')
    else:
        logger.warning('在会话状态中未找到 "generated_document_content"，跳过保存。')

    content_to_return = None

    if callback_context._invocation_context.agent.parent_agent:
        # 2. 创建一个函数调用，用于将控制权转移到 parent
        logger.info('正在创建 transfer_to_agent 函数调用...')
        transfer_call = types.FunctionCall(
            name='transfer_to_agent', args={'agent_name': callback_context._invocation_context.agent.parent_agent.name}
        )
        transfer_part = types.Part(function_call=transfer_call)

        # 3. 创建并返回一个新的 Content 对象，这将覆盖代理的原始响应
        # 框架会解释这个函数调用并执行控制权转移
        content_to_return = types.Content(parts=[transfer_part])
        logger.info('回调将返回一个新的 Content 对象以触发代理转移。')

    return content_to_return


# 3.2 定义文档生成代理
root_agent = LlmAgent(
    name='DocumentWriterAgent',
    model=GEMINI_MODEL,
    instruction='你是一位专业的科技文档撰写者。'
    '根据上下文内的信息，生成一篇结构清晰、内容详实的 Markdown 格式文档。'
    '文档应包含标题、引言、至少三个主要部分的副标题、以及列表或代码块。除了输出markdown文档什么也不要做',
    description='一个根据主题生成 Markdown 文档的代理。',
    # 注册回调函数
    after_agent_callback=save_document_callback,
    # 新增：将代理的最终输出保存到会话状态中，以便回调函数可以访问
    output_key='generated_document_content',
)


# --- 4. 主程序：设置并运行代理 ---
async def main():
    """
    主函数，用于设置服务、运行代理并验证结果。
    """
    # --- 4.1 设置服务和运行器 ---
    logger.info('正在设置 SessionService 和 ArtifactService...')
    session_service = InMemorySessionService()
    artifact_service = InMemoryArtifactService()  # 使用内存构件服务

    # 创建会话
    await session_service.create_session(app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID)

    # 创建运行器，并传入必要的服务
    runner = Runner(
        agent=root_agent,
        app_name=APP_NAME,
        session_service=session_service,
        artifact_service=artifact_service,  # 必须配置构件服务
    )
    logger.info('运行器（Runner）已配置完毕。')

    # --- 4.2 运行代理 ---
    topic = 'Agent Development Kit (ADK) 的核心概念'
    logger.info(f"正在运行代理，主题: '{topic}'")
    user_message = types.Content(role='user', parts=[types.Part(text=topic)])

    final_response_text = '代理未生成最终响应。'
    async for event in runner.run_async(user_id=USER_ID, session_id=SESSION_ID, new_message=user_message):
        # 现在的最终响应将来自 FinalizerAgent
        if event.is_final_response() and event.content and event.content.parts:
            final_response_text = event.content.parts[0].text
            logger.info(f"代理 '{event.author}' 已生成最终响应。")

    print('\n' + '=' * 50)
    print('代理的最终用户响应:')
    print(final_response_text)
    print('=' * 50 + '\n')

    # --- 4.3 验证构件是否已保存 ---
    logger.info(f"正在验证构件 '{ARTIFACT_FILENAME}' 是否已保存...")
    try:
        # 从构件服务中加载最新版本的构件
        saved_artifact = await artifact_service.load_artifact(
            app_name=APP_NAME,
            user_id=USER_ID,
            session_id=SESSION_ID,
            filename=ARTIFACT_FILENAME,
        )

        if saved_artifact and saved_artifact.text:
            print('✅ 验证成功！已从 ArtifactService 加载到已保存的文档:')
            print('-' * 50)
            print(saved_artifact.text)
            print('-' * 50)
        else:
            print('❌ 验证失败: 未能加载到已保存的构件或构件内容为空。')

    except Exception as e:
        print(f'❌ 验证时发生错误: {e}')


# --- 5. 运行主程序 ---
if __name__ == '__main__':
    # 在标准 Python 脚本中，我们使用 asyncio.run()
    # 如果您在 Jupyter/Colab Notebook 中运行，可以直接使用 'await main()'
    try:
        asyncio.run(main())
    except RuntimeError as e:
        if 'cannot be called from a running event loop' in str(e):
            print('检测到正在运行的事件循环 (如 Jupyter/Colab)。请在单元格中直接运行 `await main()`。')
        else:
            raise
