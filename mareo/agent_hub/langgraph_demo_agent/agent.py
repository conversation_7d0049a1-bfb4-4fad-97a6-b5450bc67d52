from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph

from mareo.common.agents.LangGraphWrapperAgent import LangGraphWrapperAgent

# 1. 定义你的 StateGraph
g = StateGraph(dict)
g.add_node('echo', lambda s: {'output': f'ACK: {s["input"]}'})
g.add_edge(START, 'echo')
g.add_edge('echo', END)

# 2. 编译 + 带 Checkpointer
compiled = g.compile(checkpointer=MemorySaver())

# 3. 包成 ADK Agent
root_agent = LangGraphWrapperAgent(name='echo_agent', compiled_graph=compiled)
