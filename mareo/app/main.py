from __future__ import annotations

import logging
import mimetypes
import os
from contextlib import asynccontextmanager
from pathlib import Path
from typing import Any, Mapping, Optional

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse
from fastapi.staticfiles import StaticFiles
from google.adk.cli.utils import cleanup, envs
from google.adk.cli.utils.agent_loader import Agent<PERSON>oa<PERSON>
from opentelemetry import trace
from opentelemetry.sdk.trace import export
from watchdog.events import FileSystemEventHandler
from watchdog.observers import Observer

from mareo.app.api.v1.endpoints import agent, debug, eval, session
from mareo.app.core.a2a_handlers import setup_a2a
from mareo.app.core.tracing import ApiServerSpanExporter, InMemoryExporter
from mareo.app.services import (
    create_artifact_service,
    create_credential_service,
    create_memory_service,
    create_session_service,
)

logger = logging.getLogger('google_adk.' + __name__)


class AgentChangeEventHandler(FileSystemEventHandler):
    def __init__(self, app: FastAPI):
        self.app = app

    def on_modified(self, event):
        if not (event.src_path.endswith('.py') or event.src_path.endswith('.yaml')):
            return
        logger.info('Change detected in agents directory: %s', event.src_path)
        self.app.state.agent_loader.remove_agent_from_cache(self.app.state._app_name)
        self.app.state._runners_to_clean.add(self.app.state._app_name)


def get_fast_api_app(
    *,
    agents_dir: str,
    session_service_uri: Optional[str] = None,
    session_db_kwargs: Optional[Mapping[str, Any]] = None,
    artifact_service_uri: Optional[str] = None,
    memory_service_uri: Optional[str] = None,
    eval_storage_uri: Optional[str] = None,
    allow_origins: Optional[list[str]] = None,
    web: bool,
    a2a: bool = False,
    host: str = '127.0.0.1',
    port: int = 8000,
    trace_to_cloud: bool = False,
    reload_agents: bool = False,
    lifespan: Optional[asynccontextmanager[Any]] = None,
) -> FastAPI:
    @asynccontextmanager
    async def internal_lifespan(app: FastAPI):
        try:
            if lifespan:
                async with lifespan(app) as lifespan_context:
                    yield lifespan_context
            else:
                yield
        finally:
            if reload_agents:
                observer.stop()
                observer.join()
            await cleanup.close_runners(list(app.state.runner_dict.values()))

    app = FastAPI(lifespan=internal_lifespan)

    # App state
    app.state.agents_dir = agents_dir
    app.state.session_service_uri = session_service_uri
    app.state.session_db_kwargs = session_db_kwargs
    app.state.artifact_service_uri = artifact_service_uri
    app.state.memory_service_uri = memory_service_uri
    app.state.eval_storage_uri = eval_storage_uri
    app.state.host = host
    app.state.port = port
    app.state._app_name = ''
    app.state._runners_to_clean = set()
    app.state.runner_dict = {}

    # Tracing state
    app.state.trace_dict = {}
    app.state.session_trace_dict = {}

    provider = trace.get_tracer_provider()
    provider.add_span_processor(export.SimpleSpanProcessor(ApiServerSpanExporter(app.state.trace_dict)))
    app.state.memory_exporter = InMemoryExporter(app.state.session_trace_dict)
    provider.add_span_processor(export.SimpleSpanProcessor(app.state.memory_exporter))

    if trace_to_cloud:
        from opentelemetry.exporter.cloud_trace import CloudTraceSpanExporter

        envs.load_dotenv_for_agent('', agents_dir)
        if project_id := os.environ.get('GOOGLE_CLOUD_PROJECT', None):
            processor = export.BatchSpanProcessor(CloudTraceSpanExporter(project_id=project_id))
            provider.add_span_processor(processor)
        else:
            logger.warning('GOOGLE_CLOUD_PROJECT environment variable is not set. Tracing will not be enabled.')

    # trace.set_tracer_provider(provider)

    app.state.agent_loader = AgentLoader(agents_dir)
    observer = Observer()
    if reload_agents:
        event_handler = AgentChangeEventHandler(app)
        observer.schedule(event_handler, agents_dir, recursive=True)
        observer.start()

    if allow_origins:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=allow_origins,
            allow_credentials=True,
            allow_methods=['*'],
            allow_headers=['*'],
        )

    # Services
    app.state.memory_service = create_memory_service(memory_service_uri)
    app.state.session_service = create_session_service(session_service_uri, session_db_kwargs)
    app.state.artifact_service = create_artifact_service(artifact_service_uri)
    app.state.credential_service = create_credential_service()

    app.include_router(agent.router, tags=['agent'])
    app.include_router(debug.router, tags=['debug'])
    app.include_router(eval.router, tags=['eval'])
    app.include_router(session.router, tags=['session'])

    if a2a:
        setup_a2a(app)

    if web:
        mimetypes.add_type('application/javascript', '.js', True)
        mimetypes.add_type('text/javascript', '.js', True)

        BASE_DIR = Path(__file__).parent.parent.parent.resolve()
        ANGULAR_DIST_PATH = BASE_DIR / 'frontend/dev_ui'

        @app.get('/')
        async def redirect_root_to_dev_ui():
            return RedirectResponse('/dev-ui/')

        @app.get('/dev-ui')
        async def redirect_dev_ui_add_slash():
            return RedirectResponse('/dev-ui/')

        app.mount(
            '/dev-ui/',
            StaticFiles(directory=ANGULAR_DIST_PATH, html=True, follow_symlink=True),
            name='static',
        )

    return app
