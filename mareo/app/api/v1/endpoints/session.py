from __future__ import annotations

import logging
from typing import Any, Optional

import graphviz
from fastapi import APIRouter, HTTPException, Query, Request
from google.adk.cli import agent_graph
from google.adk.events.event import Event
from google.adk.sessions.session import Session
from google.genai import types

from mareo.app.models.api_models import GetEventGraphResult

router = APIRouter()
logger = logging.getLogger('google_adk.' + __name__)

EVAL_SESSION_ID_PREFIX = 'eval-'


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}',
    response_model_exclude_none=True,
)
async def get_session(app_name: str, user_id: str, session_id: str, request: Request) -> Session:
    session_service = request.app.state.session_service
    session = await session_service.get_session(app_name=app_name, user_id=user_id, session_id=session_id)
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')
    return session


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions',
    response_model_exclude_none=True,
)
async def list_sessions(app_name: str, user_id: str, request: Request) -> list[Session]:
    session_service = request.app.state.session_service
    list_sessions_response = await session_service.list_sessions(app_name=app_name, user_id=user_id)
    return [session for session in list_sessions_response.sessions if not session.id.startswith(EVAL_SESSION_ID_PREFIX)]


@router.post(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}',
    response_model_exclude_none=True,
)
async def create_session_with_id(
    app_name: str,
    user_id: str,
    session_id: str,
    request: Request,
    state: Optional[dict[str, Any]] = None,
) -> Session:
    session_service = request.app.state.session_service
    if await session_service.get_session(app_name=app_name, user_id=user_id, session_id=session_id) is not None:
        logger.warning('Session already exists: %s', session_id)
        raise HTTPException(status_code=400, detail=f'Session already exists: {session_id}')
    logger.info('New session created: %s', session_id)
    return await session_service.create_session(app_name=app_name, user_id=user_id, state=state, session_id=session_id)


@router.post(
    '/apps/{app_name}/users/{user_id}/sessions',
    response_model_exclude_none=True,
)
async def create_session(
    app_name: str,
    user_id: str,
    request: Request,
    state: Optional[dict[str, Any]] = None,
    events: Optional[list[Event]] = None,
) -> Session:
    session_service = request.app.state.session_service
    logger.info('New session created')
    session = await session_service.create_session(app_name=app_name, user_id=user_id, state=state)

    if events:
        for event in events:
            await session_service.append_event(session=session, event=event)

    return session


@router.delete('/apps/{app_name}/users/{user_id}/sessions/{session_id}')
async def delete_session(app_name: str, user_id: str, session_id: str, request: Request):
    session_service = request.app.state.session_service
    await session_service.delete_session(app_name=app_name, user_id=user_id, session_id=session_id)


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts/{artifact_name}',
    response_model_exclude_none=True,
)
async def load_artifact(
    app_name: str,
    user_id: str,
    session_id: str,
    artifact_name: str,
    request: Request,
    version: Optional[int] = Query(None),
) -> Optional[types.Part]:
    artifact_service = request.app.state.artifact_service
    artifact = await artifact_service.load_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename=artifact_name,
        version=version,
    )
    if not artifact:
        raise HTTPException(status_code=404, detail='Artifact not found')
    return artifact


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts/{artifact_name}/versions/{version_id}',
    response_model_exclude_none=True,
)
async def load_artifact_version(
    app_name: str,
    user_id: str,
    session_id: str,
    artifact_name: str,
    version_id: int,
    request: Request,
) -> Optional[types.Part]:
    artifact_service = request.app.state.artifact_service
    artifact = await artifact_service.load_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename=artifact_name,
        version=version_id,
    )
    if not artifact:
        raise HTTPException(status_code=404, detail='Artifact not found')
    return artifact


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts',
    response_model_exclude_none=True,
)
async def list_artifact_names(app_name: str, user_id: str, session_id: str, request: Request) -> list[str]:
    artifact_service = request.app.state.artifact_service
    return await artifact_service.list_artifact_keys(app_name=app_name, user_id=user_id, session_id=session_id)


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts/{artifact_name}/versions',
    response_model_exclude_none=True,
)
async def list_artifact_versions(
    app_name: str,
    user_id: str,
    session_id: str,
    artifact_name: str,
    request: Request,
) -> list[int]:
    artifact_service = request.app.state.artifact_service
    return await artifact_service.list_versions(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename=artifact_name,
    )


@router.delete(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts/{artifact_name}',
)
async def delete_artifact(
    app_name: str,
    user_id: str,
    session_id: str,
    artifact_name: str,
    request: Request,
):
    artifact_service = request.app.state.artifact_service
    await artifact_service.delete_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename=artifact_name,
    )


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/events/{event_id}/graph',
    response_model_exclude_none=True,
)
async def get_event_graph(
    app_name: str, user_id: str, session_id: str, event_id: str, request: Request
) -> GetEventGraphResult | dict:
    session_service = request.app.state.session_service
    session = await session_service.get_session(app_name=app_name, user_id=user_id, session_id=session_id)
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')

    event = next((e for e in session.events if e.id == event_id), None)
    if not event:
        raise HTTPException(status_code=404, detail='Event not found')

    agent_loader = request.app.state.agent_loader
    root_agent = agent_loader.load_agent(app_name)
    dot_graph = None

    function_calls = event.get_function_calls()
    if function_calls:
        highlights = [(event.author, fc.name) for fc in function_calls]
        dot_graph = await agent_graph.get_agent_graph(root_agent, highlights)
    else:
        function_responses = event.get_function_responses()
        if function_responses:
            highlights = [(fr.name, event.author) for fr in function_responses]
            dot_graph = await agent_graph.get_agent_graph(root_agent, highlights)
        else:
            dot_graph = await agent_graph.get_agent_graph(root_agent, [(event.author, '')])

    if dot_graph and isinstance(dot_graph, graphviz.Digraph):
        return GetEventGraphResult(dot_src=dot_graph.source)

    return {}
