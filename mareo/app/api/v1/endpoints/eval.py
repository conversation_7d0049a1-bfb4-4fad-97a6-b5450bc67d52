from __future__ import annotations

import logging
import time

from fastapi import APIRouter, HTTPException, Request
from google.adk.cli.utils import create_empty_state, evals
from google.adk.cli.utils.agent_loader import AgentLoader
from google.adk.errors.not_found_error import NotFoundError
from google.adk.evaluation.base_eval_service import (
    InferenceConfig,
    InferenceRequest,
)
from google.adk.evaluation.eval_case import EvalCase, SessionInput
from google.adk.evaluation.eval_result import EvalSetResult
from google.adk.evaluation.local_eval_set_results_manager import (
    LocalEvalSetResultsManager,
)
from google.adk.evaluation.local_eval_sets_manager import LocalEvalSetsManager
from mareo.app.models.api_models import (
    AddSessionToEvalSetRequest,
    RunEvalRequest,
    RunEvalResult,
)

router = APIRouter()
logger = logging.getLogger('google_adk.' + __name__)

_EVAL_SET_FILE_EXTENSION = '.evalset.json'


def get_eval_managers(request: Request):
    if request.app.state.eval_storage_uri:
        gcs_eval_managers = evals.create_gcs_eval_managers_from_uri(request.app.state.eval_storage_uri)
        return (
            gcs_eval_managers.eval_sets_manager,
            gcs_eval_managers.eval_set_results_manager,
        )
    else:
        # Use managers from app state if available (for testing), otherwise create new ones
        eval_sets_manager = getattr(request.app.state, 'eval_sets_manager', None)
        eval_set_results_manager = getattr(request.app.state, 'eval_set_results_manager', None)

        if eval_sets_manager is None:
            eval_sets_manager = LocalEvalSetsManager(agents_dir=request.app.state.agents_dir)
        if eval_set_results_manager is None:
            eval_set_results_manager = LocalEvalSetResultsManager(agents_dir=request.app.state.agents_dir)

        return eval_sets_manager, eval_set_results_manager


@router.post('/apps/{app_name}/eval_sets/{eval_set_id}', response_model_exclude_none=True)
def create_eval_set(
    app_name: str,
    eval_set_id: str,
    request: Request,
):
    """Creates an eval set, given the id."""
    eval_sets_manager, _ = get_eval_managers(request)
    try:
        eval_sets_manager.create_eval_set(app_name, eval_set_id)
    except ValueError as ve:
        raise HTTPException(
            status_code=400,
            detail=str(ve),
        ) from ve


@router.get('/apps/{app_name}/eval_sets', response_model_exclude_none=True)
def list_eval_sets(app_name: str, request: Request) -> list[str]:
    """Lists all eval sets for the given app."""
    eval_sets_manager, _ = get_eval_managers(request)
    try:
        return eval_sets_manager.list_eval_sets(app_name)
    except NotFoundError as e:
        logger.warning(e)
        return []


@router.post(
    '/apps/{app_name}/eval_sets/{eval_set_id}/add_session',
    response_model_exclude_none=True,
)
async def add_session_to_eval_set(app_name: str, eval_set_id: str, req: AddSessionToEvalSetRequest, request: Request):
    session_service = request.app.state.session_service
    eval_sets_manager, _ = get_eval_managers(request)
    agent_loader = request.app.state.agent_loader
    # Get the session
    session = await session_service.get_session(app_name=app_name, user_id=req.user_id, session_id=req.session_id)
    assert session, 'Session not found.'

    # Convert the session data to eval invocations
    invocations = evals.convert_session_to_eval_invocations(session)

    # Populate the session with initial session state.
    initial_session_state = create_empty_state(agent_loader.load_agent(app_name))

    new_eval_case = EvalCase(
        eval_id=req.eval_id,
        conversation=invocations,
        session_input=SessionInput(app_name=app_name, user_id=req.user_id, state=initial_session_state),
        creation_timestamp=time.time(),
    )

    try:
        eval_sets_manager.add_eval_case(app_name, eval_set_id, new_eval_case)
    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve)) from ve


@router.get('/apps/{app_name}/eval_sets/{eval_set_id}/evals', response_model_exclude_none=True)
def list_evals_in_eval_set(app_name: str, eval_set_id: str, request: Request) -> list[str]:
    """Lists all evals in an eval set."""
    eval_sets_manager, _ = get_eval_managers(request)
    eval_set_data = eval_sets_manager.get_eval_set(app_name, eval_set_id)

    if not eval_set_data:
        raise HTTPException(status_code=400, detail=f'Eval set `{eval_set_id}` not found.')

    return sorted([x.eval_id for x in eval_set_data.eval_cases])


@router.get(
    '/apps/{app_name}/eval_sets/{eval_set_id}/evals/{eval_case_id}',
    response_model_exclude_none=True,
)
def get_eval(
    app_name: str,
    eval_set_id: str,
    eval_case_id: str,
    request: Request,
) -> EvalCase:
    """Gets an eval case in an eval set."""
    eval_sets_manager, _ = get_eval_managers(request)
    eval_case_to_find = eval_sets_manager.get_eval_case(app_name, eval_set_id, eval_case_id)

    if eval_case_to_find:
        return eval_case_to_find

    raise HTTPException(
        status_code=404,
        detail=f'Eval set `{eval_set_id}` or Eval `{eval_case_id}` not found.',
    )


@router.put(
    '/apps/{app_name}/eval_sets/{eval_set_id}/evals/{eval_case_id}',
    response_model_exclude_none=True,
)
def update_eval(
    app_name: str,
    eval_set_id: str,
    eval_case_id: str,
    updated_eval_case: EvalCase,
    request: Request,
):
    if updated_eval_case.eval_id and updated_eval_case.eval_id != eval_case_id:
        raise HTTPException(
            status_code=400,
            detail=('Eval id in EvalCase should match the eval id in the API route.'),
        )

    updated_eval_case.eval_id = eval_case_id
    eval_sets_manager, _ = get_eval_managers(request)
    try:
        eval_sets_manager.update_eval_case(app_name, eval_set_id, updated_eval_case)
    except NotFoundError as nfe:
        raise HTTPException(status_code=404, detail=str(nfe)) from nfe


@router.delete('/apps/{app_name}/eval_sets/{eval_set_id}/evals/{eval_case_id}')
def delete_eval(
    app_name: str,
    eval_set_id: str,
    eval_case_id: str,
    request: Request,
):
    eval_sets_manager, _ = get_eval_managers(request)
    try:
        eval_sets_manager.delete_eval_case(app_name, eval_set_id, eval_case_id)
    except NotFoundError as nfe:
        raise HTTPException(status_code=404, detail=str(nfe)) from nfe


@router.post(
    '/apps/{app_name}/eval_sets/{eval_set_id}/run_eval',
    response_model_exclude_none=True,
)
async def run_eval(app_name: str, eval_set_id: str, req: RunEvalRequest, request: Request) -> list[RunEvalResult]:
    """Runs an eval given the details in the eval request."""
    try:
        from google.adk.cli.cli_eval import (
            MISSING_EVAL_DEPENDENCIES_MESSAGE,
            _collect_eval_results,
            _collect_inferences,
        )
        from google.adk.evaluation.local_eval_service import LocalEvalService

        eval_sets_manager, eval_set_results_manager = get_eval_managers(request)
        eval_set = eval_sets_manager.get_eval_set(app_name, eval_set_id)

        if not eval_set:
            raise HTTPException(status_code=400, detail=f'Eval set `{eval_set_id}` not found.')

        agent_loader = request.app.state.agent_loader
        root_agent = agent_loader.load_agent(app_name)
        session_service = request.app.state.session_service
        artifact_service = request.app.state.artifact_service

        eval_service = LocalEvalService(
            root_agent=root_agent,
            eval_sets_manager=eval_sets_manager,
            eval_set_results_manager=eval_set_results_manager,
            session_service=session_service,
            artifact_service=artifact_service,
        )
        inference_request = InferenceRequest(
            app_name=app_name,
            eval_set_id=eval_set.eval_set_id,
            eval_case_ids=req.eval_ids,
            inference_config=InferenceConfig(),
        )
        inference_results = await _collect_inferences(inference_requests=[inference_request], eval_service=eval_service)

        eval_case_results = await _collect_eval_results(
            inference_results=inference_results,
            eval_service=eval_service,
            eval_metrics=req.eval_metrics,
        )
    except ModuleNotFoundError as e:
        logger.exception('%s', e)
        raise HTTPException(status_code=400, detail=MISSING_EVAL_DEPENDENCIES_MESSAGE) from e

    run_eval_results = []
    for eval_case_result in eval_case_results:
        run_eval_results.append(
            RunEvalResult(
                eval_set_file=eval_case_result.eval_set_file,
                eval_set_id=eval_set_id,
                eval_id=eval_case_result.eval_id,
                final_eval_status=eval_case_result.final_eval_status,
                overall_eval_metric_results=eval_case_result.overall_eval_metric_results,
                eval_metric_result_per_invocation=eval_case_result.eval_metric_result_per_invocation,
                user_id=eval_case_result.user_id,
                session_id=eval_case_result.session_id,
            )
        )

    return run_eval_results


@router.get('/apps/{app_name}/eval_results/{eval_result_id}', response_model_exclude_none=True)
def get_eval_result(
    app_name: str,
    eval_result_id: str,
    request: Request,
) -> EvalSetResult:
    """Gets the eval result for the given eval id."""
    _, eval_set_results_manager = get_eval_managers(request)
    try:
        return eval_set_results_manager.get_eval_set_result(app_name, eval_result_id)
    except ValueError as ve:
        raise HTTPException(status_code=404, detail=str(ve)) from ve
    except Exception as ve:
        raise HTTPException(status_code=500, detail=str(ve)) from ve


@router.get('/apps/{app_name}/eval_results', response_model_exclude_none=True)
def list_eval_results(app_name: str, request: Request) -> list[str]:
    """Lists all eval results for the given app."""
    _, eval_set_results_manager = get_eval_managers(request)
    return eval_set_results_manager.list_eval_set_results(app_name)


@router.get('/apps/{app_name}/eval_metrics', response_model_exclude_none=True)
def list_eval_metrics(app_name: str, request: Request) -> list[dict]:
    """Lists available eval metrics."""
    # Return a list of available eval metrics with their metadata
    return [
        {
            'metricName': 'tool_trajectory_avg_score',
            'description': 'Average score for tool trajectory evaluation',
            'metricValueInfo': {'type': 'float', 'range': [0.0, 1.0], 'higher_is_better': True},
        },
        {
            'metricName': 'response_relevance',
            'description': 'Relevance score of the agent response',
            'metricValueInfo': {'type': 'float', 'range': [0.0, 1.0], 'higher_is_better': True},
        },
        {
            'metricName': 'task_completion',
            'description': 'Task completion success rate',
            'metricValueInfo': {'type': 'boolean', 'higher_is_better': True},
        },
    ]
