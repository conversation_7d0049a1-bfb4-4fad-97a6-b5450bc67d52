from __future__ import annotations

import os
from typing import Optional, Mapping, Any

import click

from google.adk.artifacts.gcs_artifact_service import GcsArtifactService
from google.adk.artifacts.in_memory_artifact_service import InMemoryArtifactService
from google.adk.auth.credential_service.in_memory_credential_service import InMemoryCredentialService
from google.adk.memory.in_memory_memory_service import InMemoryMemoryService
from google.adk.memory.vertex_ai_memory_bank_service import VertexAiMemoryBankService
from google.adk.sessions.in_memory_session_service import InMemorySessionService
from google.adk.sessions.vertex_ai_session_service import VertexAiSessionService
from google.adk.cli.utils import envs


def _parse_agent_engine_resource_name(agent_engine_id_or_resource_name):
    if not agent_engine_id_or_resource_name:
        raise click.ClickException('Agent engine resource name or resource id can not be empty.')

    # "projects/my-project/locations/us-central1/reasoningEngines/**********",
    if '/' in agent_engine_id_or_resource_name:
        # Validate resource name.
        if len(agent_engine_id_or_resource_name.split('/')) != 6:
            raise click.ClickException(
                'Agent engine resource name is mal-formatted. It should be of'
                ' format :'
                ' projects/{project_id}/locations/{location}/reasoningEngines/{resource_id}'
            )
        project = agent_engine_id_or_resource_name.split('/')[1]
        location = agent_engine_id_or_resource_name.split('/')[3]
        agent_engine_id = agent_engine_id_or_resource_name.split('/')[-1]
    else:
        envs.load_dotenv_for_agent('', '')
        project = os.environ['GOOGLE_CLOUD_PROJECT']
        location = os.environ['GOOGLE_CLOUD_LOCATION']
        agent_engine_id = agent_engine_id_or_resource_name
    return project, location, agent_engine_id


def create_memory_service(memory_service_uri: Optional[str]):
    if memory_service_uri:
        if memory_service_uri.startswith('rag://'):
            from google.adk.memory.vertex_ai_rag_memory_service import VertexAiRagMemoryService

            rag_corpus = memory_service_uri.split('://')[1]
            if not rag_corpus:
                raise click.ClickException('Rag corpus can not be empty.')
            envs.load_dotenv_for_agent('', '')
            return VertexAiRagMemoryService(
                rag_corpus=f'projects/{os.environ["GOOGLE_CLOUD_PROJECT"]}/locations/{os.environ["GOOGLE_CLOUD_LOCATION"]}/ragCorpora/{rag_corpus}'
            )
        elif memory_service_uri.startswith('agentengine://'):
            agent_engine_id_or_resource_name = memory_service_uri.split('://')[1]
            project, location, agent_engine_id = _parse_agent_engine_resource_name(agent_engine_id_or_resource_name)
            return VertexAiMemoryBankService(
                project=project,
                location=location,
                agent_engine_id=agent_engine_id,
            )
        else:
            raise click.ClickException('Unsupported memory service URI: %s' % memory_service_uri)
    return InMemoryMemoryService()


def create_session_service(session_service_uri: Optional[str], session_db_kwargs: Optional[Mapping[str, Any]]):
    if session_service_uri:
        if session_service_uri.startswith('agentengine://'):
            agent_engine_id_or_resource_name = session_service_uri.split('://')[1]
            project, location, agent_engine_id = _parse_agent_engine_resource_name(agent_engine_id_or_resource_name)
            return VertexAiSessionService(
                project=project,
                location=location,
                agent_engine_id=agent_engine_id,
            )
        else:
            from google.adk.sessions.database_session_service import DatabaseSessionService

            # Database session additional settings
            if session_db_kwargs is None:
                session_db_kwargs = {}
            return DatabaseSessionService(db_url=session_service_uri, **session_db_kwargs)
    return InMemorySessionService()


def create_artifact_service(artifact_service_uri: Optional[str]):
    if artifact_service_uri:
        if artifact_service_uri.startswith('gs://'):
            gcs_bucket = artifact_service_uri.split('://')[1]
            return GcsArtifactService(bucket_name=gcs_bucket)
        else:
            raise click.ClickException('Unsupported artifact service URI: %s' % artifact_service_uri)
    return InMemoryArtifactService()


def create_credential_service():
    return InMemoryCredentialService()
