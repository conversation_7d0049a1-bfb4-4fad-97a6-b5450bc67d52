from __future__ import annotations
import typing
from opentelemetry.sdk.trace import export, ReadableSpan
from typing_extensions import override


class ApiServerSpanExporter(export.SpanExporter):
    def __init__(self, trace_dict):
        self.trace_dict = trace_dict

    def export(self, spans: typing.Sequence[ReadableSpan]) -> export.SpanExportResult:
        for span in spans:
            if span.name == 'call_llm' or span.name == 'send_data' or span.name.startswith('execute_tool'):
                attributes = dict(span.attributes)
                attributes['trace_id'] = span.get_span_context().trace_id
                attributes['span_id'] = span.get_span_context().span_id
                if attributes.get('gcp.vertex.agent.event_id', None):
                    self.trace_dict[attributes['gcp.vertex.agent.event_id']] = attributes
        return export.SpanExportResult.SUCCESS

    def force_flush(self, timeout_millis: int = 30000) -> bool:
        return True


class InMemoryExporter(export.SpanExporter):
    def __init__(self, trace_dict):
        super().__init__()
        self._spans = []
        self.trace_dict = trace_dict

    @override
    def export(self, spans: typing.Sequence[ReadableSpan]) -> export.SpanExportResult:
        for span in spans:
            trace_id = span.context.trace_id
            if span.name == 'call_llm':
                attributes = dict(span.attributes)
                session_id = attributes.get('gcp.vertex.agent.session_id', None)
                if session_id:
                    if session_id not in self.trace_dict:
                        self.trace_dict[session_id] = [trace_id]
                    else:
                        self.trace_dict[session_id] += [trace_id]
        self._spans.extend(spans)
        return export.SpanExportResult.SUCCESS

    @override
    def force_flush(self, timeout_millis: int = 30000) -> bool:
        return True

    def get_finished_spans(self, session_id: str):
        trace_ids = self.trace_dict.get(session_id, None)
        if trace_ids is None or not trace_ids:
            return []
        return [x for x in self._spans if x.context.trace_id in trace_ids]

    def clear(self):
        self._spans.clear()
